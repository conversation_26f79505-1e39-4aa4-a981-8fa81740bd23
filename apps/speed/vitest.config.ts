import path from 'path';
import { defineProject, mergeConfig } from 'vitest/config';
import sharedConfig from '../../vitest.shared';

// Merge the shared config with the project-specific config
export default mergeConfig(
  sharedConfig,
  defineProject({
    test: {
      // App-specific settings
      name: 'speed', // Explicitly name the project
      setupFiles: './setup-test.ts',
      // Path aliases for Speed app
      alias: {
        '~': path.resolve(__dirname, './src'),
      },
      // Include both .test.tsx and .spec.tsx files
      include: ['./src/**/*.{test,spec}.{ts,tsx}'],
    },
  }),
);
