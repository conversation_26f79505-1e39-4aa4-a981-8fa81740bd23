import { createFileRoute } from '@tanstack/react-router';
import Fuse from 'fuse.js';
import { useEffect, useRef } from 'react';
import type { GroupBase } from 'react-select';
import AsyncSelect, { AsyncProps } from 'react-select/async';

export const Route = createFileRoute('/pricing/')({
  component: PricingIndexComponent,
});

interface CardData {
  json_id: string;
  name: string;
  number: string;
  set_code: string;
}

interface CardOption {
  label: CardData['name'];
  value: CardData['json_id'];
}

function PricingIndexComponent() {
  const fuseRef = useRef<Fuse<CardData>>(null);

  useEffect(() => {
    void (async () => {
      const cardDataCache = await caches.open('card_data');
      const res = await cardDataCache.match(
        import.meta.env.VITE_MTG_CARD_DATA_URL,
      );
      let cardData;
      if (res?.ok) {
        console.log('Using cached card data');
        cardData = await res.json();
      } else {
        cardData = await handleDownloadLatestCardData();
      }

      fuseRef.current = new Fuse(cardData, {
        keys: ['name'],
        threshold: 0.3,
        distance: 20,
      });
    })();
  }, []);

  const handleDownloadLatestCardData = async () => {
    console.log('Downloading latest card data');
    const res = await fetch(import.meta.env.VITE_MTG_CARD_DATA_URL);
    const clonedRes = res.clone();
    if (res.ok) {
      const cache = await caches.open('card_data');
      await cache.put(import.meta.env.VITE_MTG_CARD_DATA_URL, res);

      return clonedRes.json();
    }
  };

  const handleSearch: AsyncProps<
    CardOption,
    true,
    GroupBase<CardOption>
  >['loadOptions'] = (inputValue, callback) => {
    const results = fuseRef.current?.search(inputValue);
    if (results) {
      callback(
        results.slice(0, 100).map((result) => {
          return {
            label: result.item.name,
            value: result.item.json_id,
          };
        }),
      );
      console.log(results.length);
    }
  };

  return (
    <div>
      <h2>Pricing page</h2>
      <div className="flex gap-4">
        <AsyncSelect<CardOption, true>
          className="flex-1"
          classNames={{
            menu: () => '!w-7/12',
          }}
          loadOptions={handleSearch}
          isMulti
          controlShouldRenderValue={false}
          menuIsOpen
        />
        <button onClick={handleDownloadLatestCardData}>
          Download latest card data
        </button>
      </div>
    </div>
  );
}
