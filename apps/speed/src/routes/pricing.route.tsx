import { Outlet, createFileRoute } from '@tanstack/react-router';
import { postsQueryOptions } from '../utils/posts';

export const Route = createFileRoute('/pricing')({
  loader: async ({ context }) => {
    await context.queryClient.ensureQueryData(postsQueryOptions());
  },
  head: () => ({
    meta: [{ title: 'Pricing' }],
  }),
  component: PricingComponent,
});

function PricingComponent() {
  return (
    <div className="container mx-auto">
      <Outlet />
    </div>
  );
}
