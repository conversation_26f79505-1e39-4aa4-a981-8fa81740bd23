import { capitalize } from '../helpers/fmt';

/** @todo move this to the backend */
export enum Rarity {
  BASIC_LAND = 'basic land',
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  MYTHIC_RARE = 'mythic rare',
  SPECIAL = 'special',
}

/** @todo move this to the backend */
export type RarityKey = keyof typeof Rarity;

export function rarityFromAPI(search: string): Rarity {
  if (!search) {
    console.error(`Invalid rarity ${search} converted to COMMON`);
    return Rarity.COMMON;
  }
  const rarity = search.toLowerCase() as Rarity;
  if (!rarity) {
    // TODO: Add Bugsnag notifier when we work out how to get that working without causing Je<PERSON> to lose it.
    console.error(`Invalid rarity ${search} converted to COMMON`);
    return Rarity.COMMON;
  }
  return rarity;
}

export function rarityToString(rarity: Rarity) {
  return capitalize(rarity);
}
