import { describe, expect, it } from 'vitest';
import {
  capitalize,
  capitalizeWord,
  commaSeparated,
  humanizeKey,
  pluralize,
  pluralizedCards,
  pluralizedCardsCapitalized,
  quantityOf,
} from './fmt';

describe('capitalizeWord', () => {
  it('capitalizes the first letter of a string', () => {
    expect(capitalizeWord('hello')).toBe('Hello');
    expect(capitalizeWord('world')).toBe('World');
    expect(capitalizeWord('test')).toBe('Test');
  });

  it('handles empty strings', () => {
    expect(capitalizeWord('')).toBe('');
  });
});

describe('capitalize', () => {
  it('capitalizes each word in a sentence', () => {
    expect(capitalize('hello world')).toBe('Hello World');
    expect(capitalize('the quick brown fox')).toBe('The Quick Brown Fox');
  });

  it('handles single words', () => {
    expect(capitalize('hello')).toBe('Hello');
    expect(capitalize('test')).toBe('Test');
  });

  it('filters out empty words from multiple spaces', () => {
    expect(capitalize('hello  world')).toBe('Hello World');
    expect(capitalize('  hello   world  ')).toBe('Hello World');
  });

  it('handles empty strings', () => {
    expect(capitalize('')).toBe('');
  });

  it('handles null and undefined', () => {
    expect(capitalize(null as unknown as string)).toBe('');
    expect(capitalize(undefined as unknown as string)).toBe('');
  });

  it('handles strings with only spaces', () => {
    expect(capitalize('   ')).toBe('');
  });

  it('handles mixed case input', () => {
    expect(capitalize('hELLo WoRLd')).toBe('HELLo WoRLd');
  });
});

describe('humanizeKey', () => {
  it('converts snake_case to human readable format', () => {
    expect(humanizeKey('user_name')).toBe('User Name');
    expect(humanizeKey('first_name_last_name')).toBe('First Name Last Name');
  });

  it('converts kebab-case to human readable format', () => {
    expect(humanizeKey('user-name')).toBe('User Name');
    expect(humanizeKey('first-name-last-name')).toBe('First Name Last Name');
  });

  it('handles mixed separators', () => {
    expect(humanizeKey('user_name-test')).toBe('User Name Test');
    expect(humanizeKey('first-name_last-name')).toBe('First Name Last Name');
  });

  it('handles single words', () => {
    expect(humanizeKey('username')).toBe('Username');
    expect(humanizeKey('test')).toBe('Test');
  });

  it('handles empty strings', () => {
    expect(humanizeKey('')).toBe('');
  });

  it('handles strings with no separators', () => {
    expect(humanizeKey('hello')).toBe('Hello');
  });
});

describe('pluralize', () => {
  it('returns singular form when count is 1', () => {
    expect(pluralize('card', 1)).toBe('card');
    expect(pluralize('item', 1)).toBe('item');
    expect(pluralize('user', 1)).toBe('user');
  });

  it('returns plural form when count is not 1', () => {
    expect(pluralize('card', 0)).toBe('cards');
    expect(pluralize('card', 2)).toBe('cards');
    expect(pluralize('card', 10)).toBe('cards');
    expect(pluralize('item', 5)).toBe('items');
  });

  it('handles negative counts as plural', () => {
    expect(pluralize('card', -1)).toBe('cards');
    expect(pluralize('item', -5)).toBe('items');
  });

  it('handles decimal counts as plural', () => {
    expect(pluralize('card', 1.5)).toBe('cards');
    expect(pluralize('card', 0.5)).toBe('cards');
  });
});

describe('commaSeparated', () => {
  it('formats numbers with commas for thousands', () => {
    expect(commaSeparated(1000)).toBe('1,000');
    expect(commaSeparated(10000)).toBe('10,000');
    expect(commaSeparated(100000)).toBe('100,000');
    expect(commaSeparated(1000000)).toBe('1,000,000');
  });

  it('handles numbers less than 1000', () => {
    expect(commaSeparated(1)).toBe('1');
    expect(commaSeparated(10)).toBe('10');
    expect(commaSeparated(100)).toBe('100');
    expect(commaSeparated(999)).toBe('999');
  });

  it('handles zero and undefined', () => {
    expect(commaSeparated(0)).toBe('0');
    expect(commaSeparated(undefined)).toBe('0');
  });

  it('handles large numbers', () => {
    expect(commaSeparated(1234567890)).toBe('1,234,567,890');
    expect(commaSeparated(9876543210)).toBe('9,876,543,210');
  });

  it('handles negative numbers', () => {
    expect(commaSeparated(-1000)).toBe('-1,000');
    expect(commaSeparated(-1234567)).toBe('-1,234,567');
  });
});

describe('quantityOf', () => {
  it('returns formatted quantity with singular word when count is 1', () => {
    expect(quantityOf('card', 1)).toBe('1 card');
    expect(quantityOf('item', 1)).toBe('1 item');
  });

  it('returns formatted quantity with plural word when count is not 1', () => {
    expect(quantityOf('card', 0)).toBe('No cards');
    expect(quantityOf('card', 2)).toBe('2 cards');
    expect(quantityOf('card', 1000)).toBe('1,000 cards');
  });

  it('respects humanizeZero parameter', () => {
    expect(quantityOf('card', 0, true)).toBe('No cards');
    expect(quantityOf('card', 0, false)).toBe('0 cards');
    expect(quantityOf('card', -1, true)).toBe('No cards');
    expect(quantityOf('card', -1, false)).toBe('-1 cards');
  });

  it('formats large numbers with commas', () => {
    expect(quantityOf('card', 1234)).toBe('1,234 cards');
    expect(quantityOf('item', 1000000)).toBe('1,000,000 items');
  });
});

describe('pluralizedCards', () => {
  it('returns formatted card quantity without humanizing zero', () => {
    expect(pluralizedCards(0)).toBe('0 cards');
    expect(pluralizedCards(1)).toBe('1 card');
    expect(pluralizedCards(2)).toBe('2 cards');
    expect(pluralizedCards(1000)).toBe('1,000 cards');
  });
});

describe('pluralizedCardsCapitalized', () => {
  it('returns formatted card quantity without humanizing zero', () => {
    expect(pluralizedCardsCapitalized(0)).toBe('0 cards');
    expect(pluralizedCardsCapitalized(1)).toBe('1 card');
    expect(pluralizedCardsCapitalized(2)).toBe('2 cards');
    expect(pluralizedCardsCapitalized(1000)).toBe('1,000 cards');
  });

  it('should behave identically to pluralizedCards', () => {
    const testCases = [0, 1, 2, 5, 100, 1000, 1234567];

    testCases.forEach((count) => {
      expect(pluralizedCardsCapitalized(count)).toBe(pluralizedCards(count));
    });
  });
});
