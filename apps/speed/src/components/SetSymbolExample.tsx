import { Rarity } from '../models/Rarity';
import { SetSymbol, SetSymbolSize } from './SetSymbol';

export function SetSymbolExample() {
  return (
    <div className="p-4">
      <h3 className="text-lg font-bold mb-4">SetSymbol Component Examples</h3>

      <div className="space-y-4">
        <div>
          <h4 className="font-semibold mb-2">Basic Set Symbol (no rarity)</h4>
          <SetSymbol
            setName="Dominaria United"
            setCode="DMU"
            hoverText={true}
          />
        </div>

        <div>
          <h4 className="font-semibold mb-2">Set Symbol with Rarity</h4>
          <div className="flex gap-4 items-center">
            <SetSymbol
              setName="Dominaria United"
              setCode="DMU"
              rarity={Rarity.COMMON}
              hoverText={true}
              size={SetSymbolSize.SM}
            />
            <SetSymbol
              setName="Dominaria United"
              setCode="DMU"
              rarity={Rarity.UNCOMMON}
              hoverText={true}
              size={SetSymbolSize.SM}
            />
            <SetSymbol
              setName="Dominaria United"
              setCode="DMU"
              rarity={Rarity.RARE}
              hoverText={true}
              size={SetSymbolSize.SM}
            />
            <SetSymbol
              setName="Dominaria United"
              setCode="DMU"
              rarity={Rarity.MYTHIC_RARE}
              hoverText={true}
              size={SetSymbolSize.SM}
            />
          </div>
        </div>

        <div>
          <h4 className="font-semibold mb-2">Different Sizes</h4>
          <div className="flex gap-4 items-center">
            <SetSymbol
              setName="Dominaria United"
              setCode="DMU"
              rarity={Rarity.RARE}
              hoverText={true}
              size={SetSymbolSize.XS}
            />
            <SetSymbol
              setName="Dominaria United"
              setCode="DMU"
              rarity={Rarity.RARE}
              hoverText={true}
              size={SetSymbolSize.SM}
            />
            <SetSymbol
              setName="Dominaria United"
              setCode="DMU"
              rarity={Rarity.RARE}
              hoverText={true}
              size={SetSymbolSize.MD}
            />
            <SetSymbol
              setName="Dominaria United"
              setCode="DMU"
              rarity={Rarity.RARE}
              hoverText={true}
              size={SetSymbolSize.XL}
            />
          </div>
        </div>

        <div>
          <h4 className="font-semibold mb-2">With Collector Number</h4>
          <SetSymbol
            setName="Dominaria United"
            setCode="DMU"
            rarity={Rarity.RARE}
            collectorNumber="123"
            hoverText={true}
            size={SetSymbolSize.MD}
          />
        </div>
      </div>
    </div>
  );
}
