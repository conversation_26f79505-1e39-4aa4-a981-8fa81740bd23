# Tooltip Component

A reusable tooltip component built with `@floating-ui/react` that provides accessible, well-positioned tooltips for your application.

## Features

- **Accessible**: Follows WAI-ARIA guidelines with proper roles and attributes
- **Smart positioning**: Automatically positions tooltips to avoid viewport collisions
- **Multiple triggers**: Supports both hover and focus interactions
- **Keyboard navigation**: Dismissible with the Escape key
- **Flexible**: Can be controlled or uncontrolled
- **Custom triggers**: Supports custom elements via the `asChild` prop
- **TypeScript**: Fully typed with TypeScript support

## Installation

The component requires `@floating-ui/react` as a dependency:

```bash
pnpm add @floating-ui/react
```

## Basic Usage

```tsx
import { Tooltip, TooltipTrigger, TooltipContent } from '~/components/Tooltip';

function MyComponent() {
  return (
    <Tooltip>
      <TooltipTrigger>Hover me</TooltipTrigger>
      <TooltipContent>
        This is a tooltip!
      </TooltipContent>
    </Tooltip>
  );
}
```

## API Reference

### Tooltip

The root component that manages tooltip state and provides context.

#### Props

- `initialOpen?: boolean` - Initial open state (default: `false`)
- `placement?: 'top' | 'bottom' | 'left' | 'right'` - Preferred placement (default: `'top'`)
- `open?: boolean` - Controlled open state
- `onOpenChange?: (open: boolean) => void` - Callback when open state changes
- `children: React.ReactNode` - Child components

### TooltipTrigger

The element that triggers the tooltip when hovered or focused.

#### Props

- `asChild?: boolean` - Render as a child element instead of a button (default: `false`)
- All standard HTML button props when `asChild` is `false`
- All standard HTML element props when `asChild` is `true`

### TooltipContent

The tooltip content that appears when triggered.

#### Props

- All standard HTML div props
- `className?: string` - Additional CSS classes (merged with default styles)

## Examples

### Different Placements

```tsx
<Tooltip placement="bottom">
  <TooltipTrigger>Bottom tooltip</TooltipTrigger>
  <TooltipContent>Appears below the trigger</TooltipContent>
</Tooltip>
```

### Custom Trigger Element

```tsx
<Tooltip>
  <TooltipTrigger asChild>
    <span className="custom-element">Custom trigger</span>
  </TooltipTrigger>
  <TooltipContent>Tooltip for custom element</TooltipContent>
</Tooltip>
```

### Controlled State

```tsx
function ControlledTooltip() {
  const [open, setOpen] = useState(false);
  
  return (
    <Tooltip open={open} onOpenChange={setOpen}>
      <TooltipTrigger>Controlled trigger</TooltipTrigger>
      <TooltipContent>Controlled tooltip</TooltipContent>
    </Tooltip>
  );
}
```

### Rich Content

```tsx
<Tooltip>
  <TooltipTrigger>Card info</TooltipTrigger>
  <TooltipContent>
    <div className="space-y-1">
      <div className="font-semibold">Lightning Bolt</div>
      <div className="text-xs">
        <div>Cost: R</div>
        <div>Type: Instant</div>
      </div>
    </div>
  </TooltipContent>
</Tooltip>
```

## Styling

The tooltip content comes with default styles but can be customized:

```tsx
<TooltipContent className="bg-blue-900 text-blue-100">
  Custom styled tooltip
</TooltipContent>
```

Default styles include:
- Dark background with white text (light mode)
- Light background with dark text (dark mode)
- Rounded corners
- Padding and shadow
- High z-index for proper layering

## Accessibility

The component automatically handles:
- ARIA roles and attributes
- Keyboard navigation
- Focus management
- Screen reader compatibility

## Testing

The component includes comprehensive tests covering:
- Rendering
- Hover interactions
- Focus interactions
- Keyboard dismissal
- Controlled state
- Custom triggers

Run tests with:
```bash
npm test -- Tooltip.spec.tsx
```
