import { Tooltip, TooltipTrigger, TooltipContent } from './Tooltip';

export function TooltipExample() {
  return (
    <div className="p-8 space-y-4">
      <h2 className="text-xl font-bold">Tooltip Examples</h2>
      
      {/* Basic tooltip */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Basic Tooltip</h3>
        <Tooltip>
          <TooltipTrigger>Hover me</TooltipTrigger>
          <TooltipContent>
            This is a basic tooltip!
          </TooltipContent>
        </Tooltip>
      </div>

      {/* Tooltip with different placements */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Different Placements</h3>
        <div className="flex gap-4">
          <Tooltip placement="top">
            <TooltipTrigger>Top</TooltipTrigger>
            <TooltipContent>Tooltip on top</TooltipContent>
          </Tooltip>
          
          <Tooltip placement="bottom">
            <TooltipTrigger>Bottom</TooltipTrigger>
            <TooltipContent>Tooltip on bottom</TooltipContent>
          </Tooltip>
          
          <Tooltip placement="left">
            <TooltipTrigger>Left</TooltipTrigger>
            <TooltipContent>Tooltip on left</TooltipContent>
          </Tooltip>
          
          <Tooltip placement="right">
            <TooltipTrigger>Right</TooltipTrigger>
            <TooltipContent>Tooltip on right</TooltipContent>
          </Tooltip>
        </div>
      </div>

      {/* Tooltip with custom trigger using asChild */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Custom Trigger (asChild)</h3>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="inline-flex items-center px-3 py-2 bg-blue-500 text-white rounded-md cursor-pointer hover:bg-blue-600">
              Custom Element
            </span>
          </TooltipTrigger>
          <TooltipContent>
            This tooltip is attached to a custom element!
          </TooltipContent>
        </Tooltip>
      </div>

      {/* Tooltip with rich content */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Rich Content</h3>
        <Tooltip>
          <TooltipTrigger>Rich Content</TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <div className="font-semibold">Card Information</div>
              <div className="text-xs">
                <div>Name: Lightning Bolt</div>
                <div>Cost: R</div>
                <div>Type: Instant</div>
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
}
