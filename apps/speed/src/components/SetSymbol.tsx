import { Rarity } from '~/models/Rarity';
import { CoreAssets } from '../helpers/core_assets';
import { capitalize } from '../helpers/fmt';

export enum SetSymbolSize {
  XS = 'xs',
  SM = 'sm',
  MD = 'md',
  XL = 'xl',
}

export interface SetSymbolProps {
  setName: string;
  setCode: string;
  hoverText: boolean;
  collectorNumber?: string;
  rarity?: Rarity;
  size?: SetSymbolSize;
}

const RARITY_CHAR_MAP = {
  [Rarity.BASIC_LAND]: 'C',
  [Rarity.COMMON]: 'C',
  [Rarity.UNCOMMON]: 'U',
  [Rarity.RARE]: 'R',
  [Rarity.MYTHIC_RARE]: 'M',
  [Rarity.SPECIAL]: 'S',
};

export function rarityToChar(rarity?: Rarity) {
  switch (rarity) {
    case Rarity.BASIC_LAND:
    case Rarity.COMMON:
      return 'C';
    case Rarity.UNCOMMON:
      return 'U';
    case Rarity.RARE:
      return 'R';
    case Rarity.MYTHIC_RARE:
      return 'M';
    case Rarity.SPECIAL:
      return 'S';
    default:
      // TODO: Add Bugsnag notifier when we work out how to get that working without causing Jest to lose it.
      console.error(`Invalid rarity ${rarity} converted to COMMON`);
      return undefined;
  }
}

export function SetSymbol(props: SetSymbolProps) {
  const size = props.size === undefined ? SetSymbolSize.XS : props.size;
  const char = RARITY_CHAR_MAP[props.rarity ?? ''];
  const rarityHoverMessage = props.rarity ? capitalize(props.rarity) : '';
  const collectorNumberText = props.collectorNumber
    ? ' - ' + props.collectorNumber
    : '';
  const imgSrc =
    char === undefined
      ? `${CoreAssets.iconHost()}/set_symbols/${props.setCode}_large.png`
      : `${CoreAssets.iconHost()}/set_symbols/${
          props.setCode
        }_${char}_large.png`;

  return (
    <div className={`flex set-symbol__${size}`}>
      <img src={imgSrc} alt={`${props.setName} set symbol`} />
      {props.hoverText && hover ? (
        <div className="set-symbol-tooltip-container">
          <div className="set-symbol-tooltip">
            <div>{props.setName}</div>
            <div>{rarityHoverMessage + collectorNumberText}</div>
          </div>
        </div>
      ) : null}
    </div>
  );
}
