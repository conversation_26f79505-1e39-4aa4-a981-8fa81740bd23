# @cardcastle/react-query-client

A React Query client package for CardCastle applications, providing pre-configured API clients and React Query hooks.

## Installation

This package is part of the CardCastle monorepo and is published as a scoped package. To use it in your project, you'll need to configure your `.npmrc` file to properly resolve the package.

### Setting up .npmrc

Create or update your `.npmrc` file in your project root with the following configuration:

```ini
# .npmrc
@cardcastle:registry=https://gitlab.com/api/v4/projects/${**********************}/packages/npm/
//gitlab.com/api/v4/projects/${**********************}/packages/npm/:_authToken=${VITALIZE_PACKAGE_TOKEN}
```

Replace `${**********************}` with the ID of your GitLab project and `${VITALIZE_PACKAGE_TOKEN}` with your GitLab personal access token.

Those variables should be store in your machine local environment. For example in `~/.zshenv` or `~/.bashrc` or `~/.zshrc`.

### Installing the package

Once your `.npmrc` is configured, install the package:

```bash
# Using npm
npm install @cardcastle/react-query-client

# Using yarn
yarn add @cardcastle/react-query-client

# Using pnpm
pnpm add @cardcastle/react-query-client
```

## Usage

### Basic Setup

```typescript
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Import your API client from the package
import { apiClient } from '@cardcastle/react-query-client';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      {/* Your app components */}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
```

### Configuring Axios Base URL

The package uses axios for HTTP requests. You can configure the default `baseURL` and other axios settings:

```typescript
import { apiClient } from '@cardcastle/react-query-client';

// Configure the base URL for your API
apiClient.defaults.baseURL = 'https://api.cardcastle.com';

// You can also configure other axios defaults
apiClient.defaults.timeout = 10000;
apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;

// Or configure interceptors
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token to requests
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle common errors (e.g., redirect to login on 401)
    if (error.response?.status === 401) {
      // Redirect to login or refresh token
    }
    return Promise.reject(error);
  }
);
```

### Environment-specific Configuration

For different environments, you can set the base URL conditionally:

```typescript
const baseURL = process.env.NODE_ENV === 'production'
  ? 'https://api.cardcastle.com'
  : process.env.NODE_ENV === 'staging'
  ? 'https://staging-api.cardcastle.com'
  : 'http://localhost:3000/api';

apiClient.defaults.baseURL = baseURL;
```

### Using Generated Hooks

The package provides auto-generated React Query hooks based on your OpenAPI specification:

```typescript
import { useGetUsers, useCreateUser } from '@cardcastle/react-query-client';

function UsersList() {
  const { data: users, isLoading, error } = useGetUsers();
  const createUserMutation = useCreateUser();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {users?.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
    </div>
  );
}
```

## Development

### Building the package

```bash
pnpm build
```

### Generating API client

The package uses Orval to generate React Query hooks from OpenAPI specifications:

```bash
pnpm orval
```

## Dependencies

### Runtime Dependencies

- `@tanstack/react-query`: ^5.0.0
- `axios`: ^1.6.0

### Peer Dependencies

- `@faker-js/faker`: ^9.0.0 (for mocking)
- `msw`: ^2.0.0 (for API mocking)

## Package Configuration

The package is configured with:

- **Main entry**: `dist/index.mjs`
- **Types**: `dist/index.d.ts`
- **Package manager**: pnpm@10.10.0

## Troubleshooting

### Authentication Issues

If you encounter authentication errors when installing:

1. Ensure your `.npmrc` file is in the correct location (project root)
2. Verify your authentication token is valid
3. Check that the registry URL is correct

### Package Not Found

If the package cannot be found:

1. Verify the `.npmrc` configuration matches your package registry
2. Ensure the package has been published to the specified registry
3. Check that the scope `@cardcastle` is correctly configured
